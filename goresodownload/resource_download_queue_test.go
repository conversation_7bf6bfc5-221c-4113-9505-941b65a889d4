package goresodownload

import (
	"context"
	"testing"
	"time"

	"github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestResourceDownloadQueue(t *testing.T) {
	// Initialize MongoDB for testing
	err := gomongo.InitMongoDB()
	require.NoError(t, err)

	// Create test collection
	testCol := gomongo.Coll("test", "resource_download_queue_test")

	// Clean up test collection
	defer func() {
		ctx := context.Background()
		testCol.Drop(ctx)
	}()

	// Create queue instance
	queue, err := NewResourceDownloadQueue("TestQueue", testCol)
	require.NoError(t, err)
	require.NotNil(t, queue)

	t.Run("AddToQueue", func(t *testing.T) {
		// Create test data
		objID := primitive.NewObjectID()
		changeDoc := bson.M{
			"operationType": "insert",
			"fullDocument": bson.M{
				"_id": objID,
				"Sid": "TEST123",
			},
		}
		prop := bson.M{
			"_id": objID,
			"Sid": "TEST123",
		}

		// Add to queue
		err := queue.AddToQueue(changeDoc, prop)
		assert.NoError(t, err)

		// Verify item was added
		ctx := context.Background()
		var result QueueItem
		err = testCol.FindOne(ctx, bson.M{"_id": objID}).Decode(&result)
		assert.NoError(t, err)
		assert.Equal(t, objID, result.ID)
		assert.Equal(t, "TEST123", result.Sid)
		assert.Equal(t, 1000, result.Priority) // Default priority
	})

	t.Run("GetNextBatch", func(t *testing.T) {
		// Clean up first
		ctx := context.Background()
		testCol.DeleteMany(ctx, bson.M{})

		// Add multiple items to queue
		for i := 0; i < 5; i++ {
			objID := primitive.NewObjectID()
			changeDoc := bson.M{
				"operationType": "insert",
			}
			prop := bson.M{
				"_id": objID,
				"Sid": "BATCH" + string(rune('0'+i)),
			}
			err := queue.AddToQueue(changeDoc, prop)
			require.NoError(t, err)
		}

		// Verify items were added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		t.Logf("Documents in collection: %d", count)

		// Get batch
		batch, err := queue.GetNextBatch(3)
		assert.NoError(t, err)
		t.Logf("Batch size: %d", len(batch))
		// Note: We expect at most 3 items, but might get fewer
		assert.LessOrEqual(t, len(batch), 3)
		assert.Greater(t, len(batch), 0)

		// Verify all items have the original dlShallEndTs (1970)
		// The returned items should have the original timestamp, but the database should be updated
		for _, item := range batch {
			// The returned items should still have the original 1970 timestamp
			assert.Equal(t, "1970-01-01T00:00:00Z", item.DlShallEndTs.Format(time.RFC3339))
		}

		// Verify that the database records have been updated
		// Try to get the same batch again - should return empty since dlShallEndTs was updated
		batch2, err := queue.GetNextBatch(3)
		assert.NoError(t, err)
		assert.Len(t, batch2, 0, "Second call should return empty since items were locked")
	})

	t.Run("RemoveBatchFromQueue", func(t *testing.T) {
		// Add items to queue
		var items []QueueItem
		for i := 0; i < 3; i++ {
			objID := primitive.NewObjectID()
			changeDoc := bson.M{
				"operationType": "insert",
			}
			prop := bson.M{
				"_id": objID,
				"Sid": "REMOVE" + string(rune('0'+i)),
			}
			err := queue.AddToQueue(changeDoc, prop)
			require.NoError(t, err)

			items = append(items, QueueItem{ID: objID})
		}

		// Remove batch
		err := queue.RemoveBatchFromQueue(items)
		assert.NoError(t, err)

		// Verify items were removed
		ctx := context.Background()
		for _, item := range items {
			var result QueueItem
			err := testCol.FindOne(ctx, bson.M{"_id": item.ID}).Decode(&result)
			assert.Error(t, err) // Should not find the document
		}
	})

	t.Run("GetNext", func(t *testing.T) {
		// Clean up first
		ctx := context.Background()
		testCol.DeleteMany(ctx, bson.M{})

		// Add item to queue
		objID := primitive.NewObjectID()
		changeDoc := bson.M{
			"operationType": "insert",
		}
		prop := bson.M{
			"_id": objID,
			"Sid": "SINGLE",
		}
		err := queue.AddToQueue(changeDoc, prop)
		require.NoError(t, err)

		// Verify item was added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		t.Logf("Documents in collection: %d", count)

		// Check what's actually in the database
		var storedItem QueueItem
		err = testCol.FindOne(ctx, bson.M{"_id": objID}).Decode(&storedItem)
		require.NoError(t, err)
		t.Logf("Stored item dlShallEndTs: %v, current time: %v", storedItem.DlShallEndTs, time.Now())

		// Get next item
		item, err := queue.GetNext()
		assert.NoError(t, err)
		if assert.NotNil(t, item) {
			assert.Equal(t, objID, item.ID)
			assert.Equal(t, "SINGLE", item.Sid)
			// GetNext should return the updated item with new dlShallEndTs
			assert.True(t, item.DlShallEndTs.After(time.Now().Add(-time.Minute)),
				"GetNext should return item with updated dlShallEndTs")
		}
	})

	t.Run("EmptyQueue", func(t *testing.T) {
		// Clean queue
		ctx := context.Background()
		testCol.DeleteMany(ctx, bson.M{})

		// Try to get from empty queue
		batch, err := queue.GetNextBatch(10)
		assert.NoError(t, err)
		assert.Nil(t, batch)

		item, err := queue.GetNext()
		assert.NoError(t, err)
		assert.Nil(t, item)
	})
}

func TestGetPriority(t *testing.T) {
	testCol := gomongo.Coll("test", "priority_test")
	queue, err := NewResourceDownloadQueue("PriorityTest", testCol)
	require.NoError(t, err)

	changeDoc := bson.M{"operationType": "insert"}
	prop := bson.M{"_id": primitive.NewObjectID()}

	priority := queue.getPriority(changeDoc, prop)
	assert.Equal(t, 1000, priority) // Default priority
}

func TestGetSidFromProp(t *testing.T) {
	tests := []struct {
		name     string
		prop     bson.M
		expected string
	}{
		{
			name:     "Sid field",
			prop:     bson.M{"Sid": "TEST123"},
			expected: "TEST123",
		},
		{
			name:     "sid field",
			prop:     bson.M{"sid": "test456"},
			expected: "test456",
		},
		{
			name:     "SID field",
			prop:     bson.M{"SID": "TEST789"},
			expected: "TEST789",
		},
		{
			name:     "no sid field",
			prop:     bson.M{"other": "value"},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getSidFromProp(tt.prop)
			assert.Equal(t, tt.expected, result)
		})
	}
}
