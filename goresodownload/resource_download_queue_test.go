package goresodownload

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/real-rm/gohelper"
	"github.com/real-rm/gomongo"
)

// setupQueueTest sets up the test environment for queue tests
func setupQueueTest(t *testing.T) (*gomongo.MongoCollection, func(*gomongo.MongoCollection)) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Skip("Failed to get current directory:", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Skip("Failed to get absolute path:", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Skip("Failed to setup test environment:", err)
	}

	// Create test collection
	testCol := gomongo.Coll("rni", "reso_download_queue")
	if testCol == nil {
		t.Skip("Failed to create test collection - MongoDB not available")
	}

	// Return cleanup function
	cleanup := func(coll *gomongo.MongoCollection) {
		// Clean up test collection
		if coll != nil {
			if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
				t.Errorf("Failed to cleanup test collection: %v", err)
			}
		}
	}

	return testCol, cleanup
}

func TestResourceDownloadQueue(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	testCol, cleanup := setupQueueTest(t)
	defer cleanup(testCol)

	// Create queue instance
	queue, err := NewResourceDownloadQueue(testCol)
	require.NoError(t, err)
	require.NotNil(t, queue)

	t.Run("AddToQueue", func(t *testing.T) {
		// Create test data
		objID := primitive.NewObjectID()
		changeDoc := bson.M{
			"operationType": "insert",
			"fullDocument": bson.M{
				"_id": objID,
				"Sid": "TEST123",
			},
		}

		// Add to queue with new API
		err := queue.AddToQueue(changeDoc, objID.Hex(), 1000, "TRB")
		assert.NoError(t, err)

		// Verify item was added
		ctx := context.Background()
		var result QueueItem
		err = testCol.FindOne(ctx, bson.M{"_id": objID.Hex()}).Decode(&result)
		assert.NoError(t, err)
		assert.Equal(t, objID.Hex(), result.ID)
		assert.Equal(t, 1000, result.Priority)
		assert.Equal(t, "TRB", result.Src)
	})

	t.Run("GetNextBatch", func(t *testing.T) {
		// Clean up first
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add multiple items to queue
		for i := 0; i < 5; i++ {
			objID := primitive.NewObjectID()
			changeDoc := bson.M{
				"operationType": "insert",
				"fullDocument": bson.M{
					"_id": objID,
					"Sid": "BATCH" + string(rune('0'+i)),
				},
			}
			err := queue.AddToQueue(changeDoc, objID.Hex(), 1000, "TRB")
			require.NoError(t, err)
		}

		// Verify items were added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		t.Logf("Documents in collection: %d", count)

		// Get batch
		batch, err := queue.GetNextBatch(3)
		assert.NoError(t, err)
		t.Logf("Batch size: %d", len(batch))
		// Note: We expect at most 3 items, but might get fewer
		assert.LessOrEqual(t, len(batch), 3)
		assert.Greater(t, len(batch), 0)

		// Verify all items have the original dlShallEndTs (1970)
		// The returned items should have the original timestamp, but the database should be updated
		for _, item := range batch {
			// The returned items should still have the original 1970 timestamp
			assert.Equal(t, "1970-01-01T00:00:00Z", item.DlShallEndTs.Format(time.RFC3339))
		}

		// Verify that the database records have been updated
		// Try to get the same batch again - should return the remaining 2 items (5 total - 3 already locked)
		batch2, err := queue.GetNextBatch(3)
		assert.NoError(t, err)
		assert.Len(t, batch2, 2, "Second call should return the remaining 2 unlocked items")
	})

	t.Run("RemoveBatchFromQueue", func(t *testing.T) {
		// Add items to queue
		var items []QueueItem
		for i := 0; i < 3; i++ {
			objID := primitive.NewObjectID()
			changeDoc := bson.M{
				"operationType": "insert",
				"fullDocument": bson.M{
					"_id": objID,
					"Sid": "REMOVE" + string(rune('0'+i)),
				},
			}
			err := queue.AddToQueue(changeDoc, objID.Hex(), 1000, "TRB")
			require.NoError(t, err)

			items = append(items, QueueItem{Src: "TRB", ID: objID.Hex()})
		}

		// Remove batch
		err := queue.RemoveBatchFromQueue(items)
		assert.NoError(t, err)

		// Verify items were removed
		ctx := context.Background()
		for _, item := range items {
			var result QueueItem
			err := testCol.FindOne(ctx, bson.M{"_id": item.ID}).Decode(&result)
			assert.Error(t, err) // Should not find the document
		}
	})

	t.Run("GetNext", func(t *testing.T) {
		// Clean up first
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add item to queue
		objID := primitive.NewObjectID()
		changeDoc := bson.M{
			"operationType": "insert",
			"fullDocument": bson.M{
				"_id": objID,
				"Sid": "SINGLE",
			},
		}
		err := queue.AddToQueue(changeDoc, objID.Hex(), 1000, "TRB")
		require.NoError(t, err)

		// Verify item was added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		t.Logf("Documents in collection: %d", count)

		// Check what's actually in the database
		var storedItem QueueItem
		err = testCol.FindOne(ctx, bson.M{"_id": objID.Hex()}).Decode(&storedItem)
		require.NoError(t, err)
		t.Logf("Stored item dlShallEndTs: %v, current time: %v", storedItem.DlShallEndTs, time.Now())

		// Get next item
		item, err := queue.GetNext()
		assert.NoError(t, err)
		if assert.NotNil(t, item) {
			assert.Equal(t, objID.Hex(), item.ID)
			assert.Equal(t, "TRB", item.Src)
			// GetNext should return the updated item with new dlShallEndTs
			assert.True(t, item.DlShallEndTs.After(time.Now().Add(-time.Minute)),
				"GetNext should return item with updated dlShallEndTs")
		}
	})

	t.Run("RemoveFromQueue", func(t *testing.T) {
		// Clean up
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add item to queue
		objID := primitive.NewObjectID()
		changeDoc := bson.M{
			"operationType": "insert",
			"fullDocument": bson.M{
				"_id": objID,
				"Sid": "REMOVE_TEST",
			},
		}
		err := queue.AddToQueue(changeDoc, objID.Hex(), 1000, "TRB")
		require.NoError(t, err)

		// Verify item was added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(1), count)

		// Create QueueItem for removal
		item := &QueueItem{
			ID:  objID.Hex(),
			Src: "TRB",
		}

		// Remove item from queue
		err = queue.RemoveFromQueue(item)
		assert.NoError(t, err)

		// Verify item was removed
		count, err = testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(0), count)
	})

	t.Run("GetNextBatch_EdgeCases", func(t *testing.T) {
		// Clean queue
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Test with batchSize <= 0 (should default to 100)
		batch, err := queue.GetNextBatch(0)
		assert.NoError(t, err)
		assert.Nil(t, batch)

		batch, err = queue.GetNextBatch(-5)
		assert.NoError(t, err)
		assert.Nil(t, batch)
	})

	t.Run("AddToQueue_EdgeCases", func(t *testing.T) {
		// Clean queue
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Test with changeDoc without fullDocument
		changeDoc := bson.M{
			"operationType": "delete",
			// No fullDocument field
		}
		err := queue.AddToQueue(changeDoc, "test-id", 500, "TEST")
		assert.NoError(t, err)

		// Verify item was added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(1), count)
	})

	t.Run("EmptyQueue", func(t *testing.T) {
		// Clean queue
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Try to get from empty queue
		batch, err := queue.GetNextBatch(10)
		assert.NoError(t, err)
		assert.Nil(t, batch)

		item, err := queue.GetNext()
		assert.NoError(t, err)
		assert.Nil(t, item)
	})
}

// TestResourceDownloadQueue_UnitTests tests the queue logic without MongoDB dependency
func TestResourceDownloadQueue_UnitTests(t *testing.T) {
	t.Run("NewResourceDownloadQueue_NilCollection", func(t *testing.T) {
		queue, err := NewResourceDownloadQueue(nil)
		assert.Error(t, err)
		assert.Nil(t, queue)
		assert.Contains(t, err.Error(), "collection cannot be nil")
	})

	t.Run("QueueItem_Structure", func(t *testing.T) {
		// Test QueueItem structure
		item := QueueItem{
			ID:           "test123",
			Src:          "TRB",
			Mt:           time.Now(),
			DlShallEndTs: time.Now().Add(30 * time.Second),
			Priority:     1000,
			ChangeDoc:    bson.M{"test": "data"},
		}

		assert.Equal(t, "TRB", item.Src)
		assert.Equal(t, "test123", item.ID)
		assert.Equal(t, 1000, item.Priority)
		assert.NotNil(t, item.ChangeDoc)
	})

	t.Run("Constants", func(t *testing.T) {
		// Test constants are defined correctly
		assert.Equal(t, 30000, DOWNLOAD_ALLOWED_MS)
		assert.True(t, DOWNLOAD_ALLOWED_MS > 0)
	})
}
