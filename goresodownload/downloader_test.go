package goresodownload

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"image/color"
	"image/jpeg"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
)

// createTestImage creates a small test image and returns it as JPEG bytes
func createTestImage() ([]byte, error) {
	// Create a 10x10 image
	img := image.NewRGBA(image.Rect(0, 0, 10, 10))

	// Fill with a simple pattern
	for y := 0; y < 10; y++ {
		for x := 0; x < 10; x++ {
			img.Set(x, y, color.RGBA{uint8(x * 25), uint8(y * 25), 128, 255})
		}
	}

	// Encode as JPEG
	var buf bytes.Buffer
	if err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: 90}); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// setupDownloaderTest sets up the test environment for downloader tests
func setupDownloaderTest(t *testing.T) (*gomongo.MongoCollection, *gomongo.MongoCollection, func()) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Create test collections
	mergedColl := gomongo.Coll("rni", "merged")
	failedColl := gomongo.Coll("rni", "failed")
	if mergedColl == nil || failedColl == nil {
		t.Fatalf("Failed to get test collections")
	}

	// Clean up collections before test
	if _, err := mergedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup merged collection: %v", err)
	}
	if _, err := failedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup failed collection: %v", err)
	}

	// Create test directories
	testDir := filepath.Join(currentDir, "test_data")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// Return collections and cleanup function
	return mergedColl, failedColl, func() {
		// Clean up collections after test
		if _, err := mergedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup merged collection: %v", err)
		}
		if _, err := failedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup failed collection: %v", err)
		}
		// Clean up test directory
		if err := os.RemoveAll(testDir); err != nil {
			t.Errorf("Failed to cleanup test directory: %v", err)
		}
	}
}

func TestNewDownloader(t *testing.T) {
	mergedColl, failedColl, cleanup := setupDownloaderTest(t)
	defer cleanup()

	tests := []struct {
		name    string
		opts    *DownloaderOptions
		wantErr bool
	}{
		{
			name: "valid options",
			opts: &DownloaderOptions{
				Config:       NewDefaultConfig(),
				StoragePaths: []string{"/tmp/test"},
				MergedCol:    mergedColl,
				FailedCol:    failedColl,
			},
			wantErr: false,
		},
		{
			name:    "nil options",
			opts:    nil,
			wantErr: false,
		},
		{
			name: "nil config",
			opts: &DownloaderOptions{
				StoragePaths: []string{"/tmp/test"},
				MergedCol:    mergedColl,
				FailedCol:    failedColl,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewDownloader(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				if tt.opts == nil {
					assert.NotNil(t, got.Config)
				} else if tt.opts.Config == nil {
					assert.NotNil(t, got.Config)
				}
			}
		})
	}
}

func TestDownloader_ProcessAnalysisResult(t *testing.T) {
	// Setup test environment
	mergedColl, failedColl, cleanup := setupDownloaderTest(t)
	defer cleanup()

	// Create test directories
	testDirs := []string{"dir1", "dir2"}
	for _, dir := range testDirs {
		dirPath := filepath.Join("test_data", dir)
		// Remove directory if it exists
		if err := os.RemoveAll(dirPath); err != nil {
			t.Fatalf("Failed to cleanup directory %s: %v", dirPath, err)
		}
		// Create fresh directory
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			t.Fatalf("Failed to create test directory %s: %v", dirPath, err)
		}
	}

	// Create test image
	testImage, err := createTestImage()
	if err != nil {
		t.Fatalf("Failed to create test image: %v", err)
	}

	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		if _, err := w.Write(testImage); err != nil {
			t.Fatalf("Failed to write test image: %v", err)
		}
	}))
	defer server.Close()

	// Test cases
	testCases := []struct {
		name          string
		result        *AnalysisResult
		expectedError bool
		setup         func(docID string) error
		verify        func(t *testing.T, docID string)
	}{
		{
			name: "successful_download_and_update",
			result: &AnalysisResult{
				ID: primitive.NewObjectID().Hex(),
				DownloadTasks: []MediaTask{
					{
						Sid:      "test123",
						MediaKey: "key1",
						URL:      server.URL + "/test.jpg",
						Type:     "image/jpeg",
						DestPath: "images/test.jpg",
					},
				},
				NewFirstPic: MediaTask{
					Sid:      "test123",
					MediaKey: "key1",
					URL:      server.URL + "/test.jpg",
					Type:     "image/jpeg",
					DestPath: "images/test.jpg",
				},
				PhoLH: []int32{123, 456},
				DocLH: []string{"789.pdf"},
			},
			expectedError: false,
			setup: func(docID string) error {
				// Insert initial document
				_, err := mergedColl.InsertOne(context.Background(), bson.M{
					"_id":   docID,
					"phoLH": []int32{},
					"docLH": []string{},
				})
				return err
			},
			verify: func(t *testing.T, docID string) {
				// Verify document was updated with correct fields
				var doc bson.M
				err := mergedColl.FindOne(context.Background(), bson.M{"_id": docID}).Decode(&doc)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				// Check PhoLH
				phoLH, ok := doc["phoLH"].(bson.A)
				if !ok {
					t.Error("phoLH field not found or wrong type")
				} else {
					expectedPhoLH := []int{123, 456}
					actualPhoLH := make([]int, len(phoLH))
					for i, v := range phoLH {
						actualPhoLH[i] = int(v.(int32))
					}
					if !reflect.DeepEqual(actualPhoLH, expectedPhoLH) {
						t.Errorf("Expected PhoLH %v, got %v", expectedPhoLH, actualPhoLH)
					}
				}

				// Check DocLH
				docLH, ok := doc["docLH"].(bson.A)
				if !ok {
					t.Error("docLH field not found or wrong type")
				} else {
					expectedDocLH := []string{"789.pdf"}
					actualDocLH := make([]string, len(docLH))
					for i, v := range docLH {
						actualDocLH[i] = v.(string)
					}
					if !reflect.DeepEqual(actualDocLH, expectedDocLH) {
						t.Errorf("Expected DocLH %v, got %v", expectedDocLH, actualDocLH)
					}
				}

				// Check tnLH (should be present and non-zero)
				tnLH, ok := doc["tnLH"].(int32)
				if !ok {
					t.Error("tnLH field not found or wrong type")
				} else if tnLH == 0 {
					t.Error("tnLH should not be zero")
				}
			},
		},
		{
			name: "successful_deletion",
			result: &AnalysisResult{
				ID: primitive.NewObjectID().Hex(),
				DeleteTasks: []DeleteTask{
					{
						Sid:      "test123",
						MediaKey: "key1",
						Path:     "images/test.jpg",
					},
				},
				PhoLH: []int32{123},
				DocLH: []string{"456.pdf"},
			},
			expectedError: false,
			setup: func(docID string) error {
				// Create test file in each directory
				for _, dir := range testDirs {
					dirPath := filepath.Join("test_data", dir, "images")
					// Remove directory if it exists
					if err := os.RemoveAll(dirPath); err != nil {
						return fmt.Errorf("failed to cleanup images directory: %w", err)
					}
					// Create fresh directory
					if err := os.MkdirAll(dirPath, 0755); err != nil {
						return fmt.Errorf("failed to create images directory: %w", err)
					}
					filePath := filepath.Join(dirPath, "test.jpg")
					if err := os.WriteFile(filePath, testImage, 0644); err != nil {
						return fmt.Errorf("failed to create test file: %w", err)
					}
				}

				// Insert initial document
				_, err := mergedColl.InsertOne(context.Background(), bson.M{
					"_id":   docID,
					"phoLH": []int32{},
					"docLH": []string{},
				})
				return err
			},
			verify: func(t *testing.T, docID string) {
				// Verify document was updated with correct fields
				var doc bson.M
				err := mergedColl.FindOne(context.Background(), bson.M{"_id": docID}).Decode(&doc)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				// Check PhoLH
				phoLH, ok := doc["phoLH"].(bson.A)
				if !ok {
					t.Error("phoLH field not found or wrong type")
				} else {
					expectedPhoLH := []int{123}
					actualPhoLH := make([]int, len(phoLH))
					for i, v := range phoLH {
						actualPhoLH[i] = int(v.(int32))
					}
					if !reflect.DeepEqual(actualPhoLH, expectedPhoLH) {
						t.Errorf("Expected PhoLH %v, got %v", expectedPhoLH, actualPhoLH)
					}
				}

				// Check DocLH
				docLH, ok := doc["docLH"].(bson.A)
				if !ok {
					t.Error("docLH field not found or wrong type")
				} else {
					expectedDocLH := []string{"456.pdf"}
					actualDocLH := make([]string, len(docLH))
					for i, v := range docLH {
						actualDocLH[i] = v.(string)
					}
					if !reflect.DeepEqual(actualDocLH, expectedDocLH) {
						t.Errorf("Expected DocLH %v, got %v", expectedDocLH, actualDocLH)
					}
				}

				// Check tnLH (should not be present for deletion)
				if _, ok := doc["tnLH"]; ok {
					t.Error("tnLH should not be present for deletion")
				}
			},
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Run setup if provided
			if tc.setup != nil {
				if err := tc.setup(tc.result.ID); err != nil {
					t.Fatalf("Setup failed: %v", err)
				}
			}

			// Create downloader instance
			downloader, err := NewDownloader(&DownloaderOptions{
				Config:       NewDefaultConfig(),
				StoragePaths: []string{filepath.Join("test_data", "dir1"), filepath.Join("test_data", "dir2")},
				MergedCol:    mergedColl,
				FailedCol:    failedColl,
			})
			if err != nil {
				t.Fatalf("Failed to create downloader: %v", err)
			}

			// Process analysis result
			_, err = downloader.ProcessAnalysisResult(tc.result)
			if tc.expectedError {
				if err == nil {
					t.Error("Expected error but got nil")
				}
			} else if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			// Run verification if provided
			if tc.verify != nil {
				tc.verify(t, tc.result.ID)
			}
		})
	}
}

func TestDownloader_ProcessAnalysisResult_ErrorCases(t *testing.T) {
	mergedColl, failedColl, cleanup := setupDownloaderTest(t)
	defer cleanup()

	downloader, err := NewDownloader(&DownloaderOptions{
		Config:       NewDefaultConfig(),
		StoragePaths: []string{"/tmp"},
		MergedCol:    mergedColl,
		FailedCol:    failedColl,
	})
	assert.NoError(t, err)

	testCases := []struct {
		name    string
		result  *AnalysisResult
		setup   func(docID string) error
		wantErr bool
	}{
		{
			name: "invalid_url",
			result: &AnalysisResult{
				ID: primitive.NewObjectID().Hex(),
				DownloadTasks: []MediaTask{{
					Sid:      "test",
					MediaKey: "key",
					URL:      "http://invalid-url",
					Type:     "image/jpeg",
					DestPath: "images/test.jpg",
				}},
			},
			setup: func(docID string) error {
				_, err := mergedColl.InsertOne(context.Background(), bson.M{"_id": docID, "phoLH": []int32{}, "docLH": []string{}})
				return err
			},
			wantErr: true,
		},
		{
			name: "invalid_dest_path",
			result: &AnalysisResult{
				ID: primitive.NewObjectID().Hex(),
				DownloadTasks: []MediaTask{{
					Sid:      "test",
					MediaKey: "key",
					URL:      "http://example.com/test.jpg",
					Type:     "image/jpeg",
					DestPath: "/invalid_path/test.jpg",
				}},
			},
			setup: func(docID string) error {
				_, err := mergedColl.InsertOne(context.Background(), bson.M{"_id": docID, "phoLH": []int32{}, "docLH": []string{}})
				return err
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.setup != nil {
				_ = tc.setup(tc.result.ID)
			}
			_, err := downloader.ProcessAnalysisResult(tc.result)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestDownloader_GenerateThumbnailAndUpdate(t *testing.T) {
	mergedColl, failedColl, cleanup := setupDownloaderTest(t)
	defer cleanup()

	// Create test directories
	testDirs := []string{"dir1", "dir2"}
	for _, dir := range testDirs {
		dirPath := filepath.Join("test_data", dir)
		// Remove directory if it exists
		if err := os.RemoveAll(dirPath); err != nil {
			t.Fatalf("Failed to cleanup directory %s: %v", dirPath, err)
		}
		// Create fresh directory
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			t.Fatalf("Failed to create test directory %s: %v", dirPath, err)
		}
	}

	// Create test image
	testImage, err := createTestImage()
	assert.NoError(t, err)

	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		if _, err := w.Write(testImage); err != nil {
			t.Fatalf("Failed to write test image: %v", err)
		}
	}))
	defer server.Close()

	// Create downloader instance
	downloader, err := NewDownloader(&DownloaderOptions{
		Config:       NewDefaultConfig(),
		StoragePaths: []string{filepath.Join("test_data", "dir1"), filepath.Join("test_data", "dir2")},
		MergedCol:    mergedColl,
		FailedCol:    failedColl,
	})
	assert.NoError(t, err)

	tests := []struct {
		name    string
		task    MediaTask
		wantErr bool
		setup   func() error
	}{
		{
			name: "invalid URL",
			task: MediaTask{
				URL:      "invalid-url",
				DestPath: "images/test.jpg",
				MediaKey: "test_key",
			},
			wantErr: true,
		},
		{
			name: "valid task",
			task: MediaTask{
				URL:      server.URL + "/test.jpg",
				DestPath: "images/test.jpg",
				MediaKey: "test_key",
			},
			wantErr: false,
			setup: func() error {
				// Create images directory in each test directory
				for _, dir := range testDirs {
					dirPath := filepath.Join("test_data", dir, "images")
					if err := os.MkdirAll(dirPath, 0755); err != nil {
						return fmt.Errorf("failed to create images directory: %w", err)
					}
				}
				return nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Run setup if provided
			if tt.setup != nil {
				if err := tt.setup(); err != nil {
					t.Fatalf("Setup failed: %v", err)
				}
			}

			hash, _, err := downloader.generateThumbnailAndUpdate(tt.task, 0)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, int32(0), hash)
			} else {
				assert.NoError(t, err)
				assert.NotEqual(t, int32(0), hash)
			}
		})
	}
}

func TestDownloader_UpdateMergedDoc(t *testing.T) {
	mergedColl, _, cleanup := setupDownloaderTest(t)
	defer cleanup()

	downloader, err := NewDownloader(&DownloaderOptions{
		Config:    NewDefaultConfig(),
		MergedCol: mergedColl,
	})
	assert.NoError(t, err)

	tests := []struct {
		name          string
		result        *AnalysisResult
		thumbnailHash int32
		wantErr       bool
	}{
		{
			name: "successful update without thumbnail",
			result: &AnalysisResult{
				ID:    primitive.NewObjectID().Hex(),
				PhoLH: []int32{1, 2, 3},
				DocLH: []string{"4.pdf", "5.pdf", "6.pdf"},
			},
			thumbnailHash: 0,
			wantErr:       false,
		},
		{
			name: "successful update with thumbnail",
			result: &AnalysisResult{
				ID:    primitive.NewObjectID().Hex(),
				PhoLH: []int32{1, 2, 3},
				DocLH: []string{"4.pdf", "5.pdf", "6.pdf"},
			},
			thumbnailHash: 12345,
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Insert a document with the target _id before update
			_, _ = mergedColl.InsertOne(context.Background(), bson.M{"_id": tt.result.ID, "phoLH": []int32{}, "docLH": []string{}})

			err := downloader.updateMergedDoc(tt.result, tt.thumbnailHash)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestDownloader_UpdateMergedDoc_ErrorCases(t *testing.T) {
	mergedColl, _, cleanup := setupDownloaderTest(t)
	defer cleanup()

	downloader, err := NewDownloader(&DownloaderOptions{
		Config:    NewDefaultConfig(),
		MergedCol: mergedColl,
	})
	assert.NoError(t, err)

	testCases := []struct {
		name    string
		result  *AnalysisResult
		thumb   int32
		setup   func(docID string) error
		wantErr bool
	}{
		{
			name:    "document_not_found",
			result:  &AnalysisResult{ID: "notfound", PhoLH: []int32{1}, DocLH: []string{"a.pdf"}},
			thumb:   0,
			setup:   nil,
			wantErr: true,
		},
		{
			name:    "nil_result",
			result:  nil,
			thumb:   0,
			setup:   nil,
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.setup != nil && tc.result != nil {
				_ = tc.setup(tc.result.ID)
			}
			err := downloader.updateMergedDoc(tc.result, tc.thumb)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Add additional test cases for edge cases and error conditions
// Example: Test invalid URL, network error, file system error
