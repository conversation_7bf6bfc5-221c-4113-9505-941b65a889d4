package goresodownload

import (
	"context"
	"fmt"
	"time"

	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	DOWNLOAD_ALLOWED_MS       = 30 * 1000 // 30 seconds
	DEFAULT_DOWNLOAD_START_TS = "1970-01-01T00:00:00Z"
)

// QueueItem represents an item in the download queue
type QueueItem struct {
	ID           primitive.ObjectID `bson:"_id,omitempty"`
	Src          string             `bson:"src"`
	Sid          string             `bson:"sid"`
	Mt           time.Time          `bson:"_mt"`
	DlShallEndTs time.Time          `bson:"dlShallEndTs"`
	Priority     int                `bson:"priority"`
	ChangeDoc    bson.M             `bson:"changeDoc"`
	Prop         bson.M             `bson:"prop"`
}

// ResourceDownloadQueue manages the download queue for resources
type ResourceDownloadQueue struct {
	resourceName string
	queueCol     *gomongo.MongoCollection
}

// NewResourceDownloadQueue creates a new ResourceDownloadQueue instance
func NewResourceDownloadQueue(resourceName string, queueCol *gomongo.MongoCollection) (*ResourceDownloadQueue, error) {
	queue := &ResourceDownloadQueue{
		resourceName: resourceName,
		queueCol:     queueCol,
	}

	// Ensure indexes
	if err := queue.ensureIndexes(); err != nil {
		return nil, fmt.Errorf("failed to ensure indexes: %w", err)
	}

	return queue, nil
}

// ensureIndexes creates necessary indexes for the queue collection
func (q *ResourceDownloadQueue) ensureIndexes() error {
	ctx := context.Background()

	// Create index on dlShallEndTs and priority
	indexModel := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "dlShallEndTs", Value: 1},
				{Key: "priority", Value: -1},
			},
			Options: options.Index().SetBackground(true),
		},
	}

	_, err := q.queueCol.CreateIndexes(ctx, indexModel)
	if err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	return nil
}

// AddToQueue adds a resource to the download queue
func (q *ResourceDownloadQueue) AddToQueue(id primitive.ObjectID, changeDoc bson.M, prop bson.M, priority int, sid string, src string) error {
	// Parse default download start time
	defaultStartTime, err := time.Parse(time.RFC3339, DEFAULT_DOWNLOAD_START_TS)
	if err != nil {
		return fmt.Errorf("failed to parse default start time: %w", err)
	}

	// Create queue item
	doc := QueueItem{
		ID:           id,
		Src:          src,
		Sid:          sid,
		Mt:           time.Now(),
		DlShallEndTs: defaultStartTime,
		Priority:     priority,
		ChangeDoc:    changeDoc,
		Prop:         prop,
	}

	// Insert or replace the document
	ctx := context.Background()

	// First try to delete existing document with same ID
	q.queueCol.DeleteOne(ctx, bson.M{"_id": doc.ID})

	// Then insert the new document
	_, err = q.queueCol.InsertOne(ctx, doc)
	if err != nil {
		return fmt.Errorf("failed to add to queue: %w", err)
	}

	golog.Info(fmt.Sprintf("%s addToQueue: successfully added", q.resourceName), "id", doc.ID, "priority", priority)
	return nil
}

// GetNextBatch gets the next batch of resources to download
func (q *ResourceDownloadQueue) GetNextBatch(batchSize int) ([]QueueItem, error) {
	if batchSize <= 0 {
		batchSize = 100
	}

	ctx := context.Background()

	// Find documents ready for download
	filter := bson.M{"dlShallEndTs": bson.M{"$lt": time.Now()}}
	opts := options.Find().
		SetSort(bson.D{{Key: "priority", Value: -1}}).
		SetLimit(int64(batchSize))

	cursor, err := q.queueCol.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find documents: %w", err)
	}
	defer cursor.Close(ctx)

	var results []QueueItem
	if err := cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode results: %w", err)
	}

	if len(results) > 0 {
		// Manually limit results if needed (fallback for driver issues)
		if len(results) > batchSize {
			results = results[:batchSize]
		}

		// Update dlShallEndTs for all records in the batch
		var ids []primitive.ObjectID
		for _, record := range results {
			ids = append(ids, record.ID)
		}

		updateFilter := bson.M{"_id": bson.M{"$in": ids}}
		updateDoc := bson.M{"$set": bson.M{"dlShallEndTs": time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)}}

		_, err := q.queueCol.UpdateMany(ctx, updateFilter, updateDoc)
		if err != nil {
			golog.Error("Failed to update dlShallEndTs for batch", "error", err)
			// Don't return error here, as we still want to process the batch
		}

		return results, nil
	}

	return nil, nil
}

// GetNext gets the next single resource to download
func (q *ResourceDownloadQueue) GetNext() (*QueueItem, error) {
	ctx := context.Background()

	filter := bson.M{"dlShallEndTs": bson.M{"$lt": time.Now()}}
	update := bson.M{"$set": bson.M{"dlShallEndTs": time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)}}
	opts := options.FindOneAndUpdate().
		SetSort(bson.D{{Key: "priority", Value: -1}}).
		SetReturnDocument(options.After)

	var result QueueItem
	err := q.queueCol.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get next item: %w", err)
	}

	return &result, nil
}

// RemoveFromQueue removes a resource from the queue
func (q *ResourceDownloadQueue) RemoveFromQueue(item *QueueItem) error {
	ctx := context.Background()

	filter := bson.M{"_id": item.ID}
	_, err := q.queueCol.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to remove from queue: %w", err)
	}

	return nil
}

// RemoveBatchFromQueue removes multiple resources from the queue
func (q *ResourceDownloadQueue) RemoveBatchFromQueue(items []QueueItem) error {
	if len(items) == 0 {
		return nil
	}

	ctx := context.Background()

	var ids []primitive.ObjectID
	for _, item := range items {
		ids = append(ids, item.ID)
	}

	filter := bson.M{"_id": bson.M{"$in": ids}}
	_, err := q.queueCol.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to remove batch from queue: %w", err)
	}

	return nil
}
